'use client';

import React, { useState, useEffect, useRef, ReactNode } from 'react';
import { X, FolderGit2, ChevronDown, CodeXml, Loader2, Github, GitBranch as GitlabIcon } from 'lucide-react';
import { PLATFORMS, backendFrameworks, frameworks, mobileFrameworks } from '@/constants/code_gen/platforms';
import { DynamicButton } from '@/components/UIComponents/Buttons/DynamicButton';
import RepositoryConfigurationPage from '@/components/Repository/RepositoryConfigurationPage';
import { SCMType } from '@/utils/scmAPI';

interface PlatformData {
    key: string;
    label: string;
    icon: React.ReactNode;
}

interface FrameworkData {
    key: string;
    label: string;
    icon?: React.ReactNode;
}

interface Repository {
    container_id: number;
    scm_id: string;
    service: string;
    repositoryName: string;
    repositoryId: string;
    cloneUrlHttp: string;
    cloneUrlSsh: string;
    organization: string;
    encrypted_scm_id: string;
    repositoryStatus: string;
}

interface CodeGenerationSetupModalProps {
    isGeneratingCode?:boolean;
    onClose: () => void;
    onConfigureRepo?: () => void; // Made optional since we'll handle it internally
    BranchSelection: React.FC;
    currentPlatform: PlatformData;
    onPlatformChange: (platformData: PlatformData) => void;
    currentFramework: FrameworkData | null;
    onFrameworkChange: (framework: FrameworkData) => void;
    onConfirm: () => void;
    repository: Repository | null;
    currentBranch: string | null;
    isModal?: boolean;
    isConfigMode?: boolean; // New prop to indicate configuration mode
    projectId: string;
    containerId: number;
    handleRepoChange?: (repo: Repository) => void;
    hideActionButton?: boolean; // New prop to hide the action button
}

interface PlatformDropdownProps {
    onChange: (platform: PlatformData) => void;
    onClose: () => void;
}

type FrameworkOption = {
    key: string;
    label: string;
    icon: ReactNode;
}

interface FrameworkDropdownProps {
    onChange: (framework: FrameworkData) => void;
    onClose: () => void;
    platformFrameworks: FrameworkOption[];
}

const PlatformDropdown: React.FC<PlatformDropdownProps> = ({
    onChange,
    onClose
}) => {
    const platformDropdownRef = useRef<HTMLDivElement | null>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (!(event.target instanceof HTMLElement)) return;
            if (
                platformDropdownRef.current &&
                !platformDropdownRef.current.contains(event.target) &&
                !document.querySelector('#platformSelection')?.contains(event.target)
            ) {
                onClose();
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onClose]);

    // Filter out fullstack platform
    const filteredPlatforms = PLATFORMS.filter(platform => platform.key !== 'fullstack');

    return (
        <div ref={platformDropdownRef} className="bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {filteredPlatforms.map((platform) => {
                return (
                    <div
                        key={platform.key}
                        className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg"
                        onClick={() => onChange(platform)}
                    >
                        <div className="flex items-center gap-3">
                            {platform.icon}
                            <span className="font-medium text-gray-800">{platform.label}</span>
                        </div>

                        {/* Clean experimental badge */}
                        {platform.key !== 'web' && platform.key !== 'mobile' && (
                            <span className="px-2 py-0.5 text-xs font-medium bg-[hsl(var(--primary))]/10 text-[hsl(var(--primary))] rounded-full">
                                Experimental
                            </span>
                        )}
                    </div>
                );
            })}
        </div>
    );
};

const FrameworkDropdown: React.FC<FrameworkDropdownProps> = ({
    onChange,
    onClose,
    platformFrameworks
}) => {
    const frameworkDropdownRef = useRef<HTMLDivElement | null>(null);
    
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (!(event.target instanceof HTMLElement)) return;
            if (
                frameworkDropdownRef.current &&
                !frameworkDropdownRef.current.contains(event.target) &&
                !document.querySelector('#frameworkSelection')?.contains(event.target)
            ) {
                onClose();
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onClose]);

    return (
        <div ref={frameworkDropdownRef} className="bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {platformFrameworks.map((framework) => (
                <div
                    key={framework.key}
                    className="flex items-center gap-3 p-3 cursor-pointer hover:bg-gray-50 transition-colors duration-150 first:rounded-t-lg last:rounded-b-lg"
                    onClick={() => onChange(framework)}
                >
                    {framework.icon}
                    <span className="font-medium text-gray-800">{framework.label}</span>
                </div>
            ))}
        </div>
    );
};

const CodeGenerationSetupModal: React.FC<CodeGenerationSetupModalProps> = ({
    onClose,
    onConfigureRepo,
    BranchSelection,
    currentPlatform,
    onPlatformChange,
    currentFramework,
    onFrameworkChange,
    onConfirm,
    repository,
    currentBranch,
    isModal = false,
    isConfigMode = false,
    isGeneratingCode,
    projectId,
    containerId,
    handleRepoChange,
    hideActionButton
}) => {
    const [isPlatformDropdownOpen, setIsPlatformDropdownOpen] = useState(false);
    const [isFrameworkDropdownOpen, setIsFrameworkDropdownOpen] = useState(false);
    const [error, setError] = useState('');
    const [showRepoConfig, setShowRepoConfig] = useState(false);
    const [selectedSCMType, setSelectedSCMType] = useState<string | null>(null);
    
    // Local repository state to track changes within the modal
    const [localRepository, setLocalRepository] = useState<Repository | null>(repository);
    const [hasRepositoryChanged, setHasRepositoryChanged] = useState(false);

    const isGenericPlatform = currentPlatform?.key === 'generic';
    const [platformFrameworks, setPlatfomFrameworks] = useState(frameworks);

    useEffect(() => {
        if (currentPlatform?.key === "mobile")
            setPlatfomFrameworks(mobileFrameworks);
        if (currentPlatform?.key === "backend")
            setPlatfomFrameworks(backendFrameworks);
        if (currentPlatform?.key === "web")
            setPlatfomFrameworks(frameworks);
    }, [currentPlatform]);

    // Update local repository when prop changes
    useEffect(() => {
        setLocalRepository(repository);
        setHasRepositoryChanged(false); // Reset change flag when repository prop changes
    }, [repository]);

    const handleSubmit = () => {
        if (!localRepository) {
            setError('Please configure repository and select a branch');
            return;
        }

        // If repository was changed, propagate changes before confirming
        if (hasRepositoryChanged && localRepository && handleRepoChange) {
            handleRepoChange(localRepository);
        }

        onConfirm();
        setError('');
    };

    const handleModalClick = (e: React.MouseEvent) => {
        e.stopPropagation();
    };

    const handleConfigureRepo = () => {
        setShowRepoConfig(true);
        setSelectedSCMType(SCMType.GITHUB); // Default to GitHub since we're only tracking basic info
    };

    const handleRepoConfigSuccess = (updatedRepo: Repository) => {
        // Update local state
        setLocalRepository(updatedRepo);
        setHasRepositoryChanged(true);
        
        // Call the parent's handler if provided
        if (handleRepoChange) {
            handleRepoChange(updatedRepo);
        }
        
        // Close the repository configuration screen
        setShowRepoConfig(false);
        
        // Close the entire modal
        handleClose();
    };

    // Enhanced close handler that updates repository state
    const handleClose = () => {
        // If repository was changed during the modal session, 
        // make sure to propagate the changes before closing
        if (hasRepositoryChanged && localRepository && handleRepoChange) {
            handleRepoChange(localRepository);
        }
        
        onClose();
    };

    // Enhanced background click handler
    const handleBackgroundClick = (e: React.MouseEvent) => {
        if (e.target === e.currentTarget && !isGeneratingCode) {
            handleClose();
        }
    };

    // The modal will prevent closing when isGeneratingCode is true
    const isFormValid = localRepository;

    // Main setup form
    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm z-50" onClick={handleBackgroundClick}>
            <div onClick={handleModalClick} className="w-[600px] max-w-[90vw] bg-white rounded-lg shadow-xl relative flex flex-col max-h-[85vh]">
                {/* Header with close button */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
                    <h2 className="font-weight-semibold typography-body-lg text-gray-800">
                        {(showRepoConfig || localRepository === null) ? 'Repository Configuration' : 'Setup Repository'}
                    </h2>
                    <button
                        onClick={handleClose}
                        className={`p-2 rounded-md transition-colors ${
                            isGeneratingCode
                                ? 'opacity-50 cursor-not-allowed'
                                : 'hover:bg-gray-100'
                        }`}
                        aria-label="Close modal"
                        disabled={isGeneratingCode}
                    >
                        <X size={16} />
                    </button>
                </div>

                {/* Scrollable content area */}
                <div className="flex-1 overflow-y-auto">
                    {(showRepoConfig || localRepository === null) ? (
                        <div className="p-6">
                            <RepositoryConfigurationPage
                                projectId={projectId}
                                containerId={containerId}
                                handleRepoChange={handleRepoConfigSuccess}
                                onSuccess={handleRepoConfigSuccess}
                                initialSCMType={selectedSCMType}
                            />
                        </div>
                    ) : (
                        <div className="p-6 space-y-6">
                        {/* Repository Section - Updated to use localRepository */}
                        <div className="space-y-3">
                            <h3 className="font-weight-semibold typography-body text-gray-700">Repository</h3>

                            {localRepository ? (
                                <div className="flex items-center justify-between border border-gray-200 rounded-lg p-3 bg-gray-50">
                                    <div className="flex items-center gap-3">
                                        <FolderGit2 size={18} className="text-gray-600" />
                                        <div>
                                            <span className="font-weight-medium text-gray-800">{localRepository.repositoryName}</span>
                                            <p className="text-xs text-gray-500 mt-0.5">Connected repository</p>
                                        </div>
                                    </div>
                                    <button
                                        onClick={handleConfigureRepo}
                                        className="typography-caption text-primary hover:text-primary-800 px-3 py-1 rounded-md hover:bg-primary-50 transition-colors"
                                        disabled={isGeneratingCode}
                                    >
                                        Change
                                    </button>
                                </div>
                            ) : (
                                <div className="grid grid-cols-2 gap-4">
                                    <button
                                        onClick={() => {
                                            setSelectedSCMType(SCMType.GITHUB);
                                            setShowRepoConfig(true);
                                        }}
                                        className="flex flex-col items-center justify-center gap-3 p-6 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 group"
                                        disabled={isGeneratingCode}
                                    >
                                        <Github size={28} className="text-gray-700 group-hover:text-gray-900" />
                                        <span className="font-medium text-gray-700 group-hover:text-gray-900">GitHub</span>
                                    </button>
                                    <button
                                        onClick={() => {
                                            setSelectedSCMType(SCMType.GITLAB);
                                            setShowRepoConfig(true);
                                        }}
                                        className="flex flex-col items-center justify-center gap-3 p-6 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 group"
                                        disabled={isGeneratingCode}
                                    >
                                        <GitlabIcon size={28} className="text-gray-700 group-hover:text-gray-900" />
                                        <span className="font-medium text-gray-700 group-hover:text-gray-900">GitLab</span>
                                    </button>
                                </div>
                            )}
                        </div>

                        {/* Branch Section */}
                        <div className="space-y-3">
                            <h3 className="font-weight-semibold typography-body text-gray-700">Branch</h3>
                            <div className="relative">
                                <BranchSelection />
                            </div>
                        </div>

                        {/* Platform Section */}
                        <div className="space-y-3">
                            <h3 className="font-weight-semibold typography-body text-gray-700">Platform</h3>
                            <div className="relative">
                                <div
                                    id="platformSelection"
                                    className={`flex items-center justify-between p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
                                        isGeneratingCode ? 'opacity-50 pointer-events-none' : ''
                                    }`}
                                    onClick={() => !isGeneratingCode && setIsPlatformDropdownOpen(!isPlatformDropdownOpen)}
                                >
                                    <div className="flex items-center gap-3">
                                        {currentPlatform.icon}
                                        <span className="font-medium">{currentPlatform.label || "Select Platform"}</span>
                                    </div>
                                    <ChevronDown
                                        size={16}
                                        className={`transition-transform duration-200 ${isPlatformDropdownOpen ? "rotate-180" : ""
                                            }`}
                                    />
                                </div>
                                {isPlatformDropdownOpen && !isGeneratingCode && (
                                    <div className="absolute top-full left-0 right-0 mt-1 z-20">
                                        <PlatformDropdown
                                            onClose={() => setIsPlatformDropdownOpen(false)}
                                            onChange={(platform) => {
                                                onPlatformChange(platform);
                                                setIsPlatformDropdownOpen(false);
                                            }}
                                        />
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Framework Section - Only show for non-generic platforms */}
                        {!isGenericPlatform && (
                            <div className="space-y-3">
                                <h3 className="font-weight-semibold typography-body text-gray-700">Framework</h3>
                                <div className="relative">
                                    <div
                                        id="frameworkSelection"
                                        className={`flex items-center justify-between p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors ${
                                            isGeneratingCode ? 'opacity-50 pointer-events-none' : ''
                                        }`}
                                        onClick={() => !isGeneratingCode && setIsFrameworkDropdownOpen(!isFrameworkDropdownOpen)}
                                    >
                                        <div className="flex items-center gap-3">
                                            {currentFramework?.icon}
                                            <span className="font-medium">{currentFramework?.label || "Select Framework"}</span>
                                        </div>
                                        <ChevronDown
                                            size={16}
                                            className={`transition-transform duration-200 ${isFrameworkDropdownOpen ? "rotate-180" : ""
                                                }`}
                                        />
                                    </div>
                                    {isFrameworkDropdownOpen && !isGeneratingCode && (
                                        <div className="absolute top-full left-0 right-0 mt-1 z-20">
                                            <FrameworkDropdown
                                                onClose={() => setIsFrameworkDropdownOpen(false)}
                                                onChange={(framework) => {
                                                    onFrameworkChange(framework);
                                                    setIsFrameworkDropdownOpen(false);
                                                }}
                                                platformFrameworks={platformFrameworks}
                                            />
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Error message if any */}
                        {error && (
                            <div className="text-red-500 typography-body-sm bg-red-50 p-3 rounded-md border border-red-200">
                                {error}
                            </div>
                        )}
                        </div>
                    )}
                </div>

                {/* Footer with action button */}
                <div className="border-t border-gray-200 p-6 flex-shrink-0">
                    {isGeneratingCode ? (
                        <div className="flex items-center justify-center py-2">
                            <Loader2 className="animate-spin mr-2" size={16} />
                            <span className="text-gray-600">Generating...</span>
                        </div>
                    ) : (
                        !hideActionButton && !showRepoConfig && localRepository && (
                            <DynamicButton
                                onClick={handleSubmit}
                                text="Start Code Generation"
                                className="w-full py-3"
                                icon={CodeXml}
                                disabled={!isFormValid || isGeneratingCode}
                                variant={isModal ? "primaryLegacy" : "primary"}
                            />
                        )
                    )}
                </div>
            </div>
        </div>
    );
};

export default CodeGenerationSetupModal;